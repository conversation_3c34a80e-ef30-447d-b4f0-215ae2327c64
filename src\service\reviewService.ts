/* eslint-disable @typescript-eslint/no-explicit-any */
import { apiClient } from '../api';
import { Path } from '../api/backendUrl';
import logger from '../utils/logger';

// Reviewer info interface for backend data structure
export interface ReviewerInfo {
  name: string;
  email: string;
}

// Review interfaces
export interface Review {
  id?: string;
  _id?: string; // MongoDB default ID field
  title: string;
  name?: string;
  email?: string;
  date: string;
  rating: number;
  review: string;
  comment?: string;
  response?: string; // Response from business owner
  images: string[];
  imageUrls: string[];
  imageNames?: string[]; // New field for storing image names
  profileImage?: string;
  serviceId?: string;
  providerId?: string;
  bookingId?: string;
  userId?: string;
  userName?: string;
  userEmail?: string;
  userProfileImage?: string;
  isVerified?: boolean;
  createdAt?: string;
  updatedAt?: string;
  serviceRating?: number;
  qualityRating?: number;
  valueRating?: number;
  communicationRating?: number;
  timelinessRating?: number;
  // Backend reviewerInfo object
  reviewerInfo?: ReviewerInfo;
}

export interface CreateReviewData {
  providerId?: string;
  serviceId?: string;
  serviceName?: string;
  bookingId?: string;
  rating: number;
  title: string;
  review: string;
  comment?: string;
  images?: File[];
  imageUrls?: string[];
  imageNames?: string[]; // New field for storing image names
  serviceRating?: number;
  qualityRating?: number;
  valueRating?: number;
  communicationRating?: number;
  timelinessRating?: number;
  userName?: string;
  userEmail?: string;
  userProfileImage?: string;
  isVerified?: boolean;
  date?: string;
  createdAt?: string;
  // Backend reviewerInfo object
  reviewerInfo?: ReviewerInfo;
}

export interface UpdateReviewData {
  rating?: number;
  title?: string;
  review?: string;
  images?: File[];
  imageUrls?: string[];
  imageNames?: string[]; // New field for storing image names
  serviceRating?: number;
  qualityRating?: number;
  valueRating?: number;
  communicationRating?: number;
  timelinessRating?: number;
}

export interface ReviewsResponse {
  reviews: Review[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  userId?: string;
}

// Utility types for image handling
export interface ImageUploadResult {
  imageName: string;
  fullUrl: string;
  key: string;
  originalName: string;
  size: number;
  timestamp: number;
}

export interface ImageDisplayData {
  imageNames: string[];
  imageUrls: string[];
}

// Type for review payload sent to backend
export interface ReviewPayload {
  providerId: string;
  serviceId: string;
  bookingId: string;
  rating: number;
  title: string;
  comment: string;
  date: string;
  serviceRating?: number;
  qualityRating?: number;
  valueRating?: number;
  communicationRating?: number;
  timelinessRating?: number;
  imageNames: string[]; // Primary field for backend storage
  imageUrls?: string[]; // Optional field for compatibility
}

/**
 * Create a new review with image names storage
 */
export const createReview = async (data: CreateReviewData): Promise<Review> => {
  try {
    console.log('Creating review with data:', {
      ...data,
      imageCount: data.imageNames?.length || 0,
      hasImageNames: !!(data.imageNames && data.imageNames.length > 0)
    });

    // Validate required fields
    // Provider ID is optional - use default if not provided
    if (!data.providerId || data.providerId.trim() === '') {
      console.warn('Provider ID not provided, using default value');
      data.providerId = 'default-provider';
    }
    if (!data.serviceId || data.serviceId.trim() === '') {
      throw new Error('Service ID is required and cannot be empty');
    }
    // Booking ID is optional for service reviews - generate one if not provided
    if (!data.bookingId || data.bookingId.trim() === '') {
      console.warn('Booking ID not provided, generating default value');
      data.bookingId = 'service-review-' + Date.now();
    }
    if (!data.rating || data.rating < 1 || data.rating > 5) {
      throw new Error('Rating must be between 1 and 5');
    }
    if (!data.title || data.title.trim() === '') {
      throw new Error('Title is required and cannot be empty');
    }
    if (!data.review || data.review.trim() === '') {
      throw new Error('Review comment is required and cannot be empty');
    }
    if (data.review.trim().length < 10 || data.review.trim().length > 2000) {
      throw new Error('Review comment must be between 10 and 2000 characters');
    }

    // Prepare review payload with image names for backend storage
    const reviewPayload: any = {
      providerId: data.providerId.trim(),
      serviceId: data.serviceId.trim(),
      bookingId: data.bookingId.trim(),
      rating: Math.round(data.rating), // Ensure integer rating
      title: data.title.trim(),
      comment: (data.comment || data.review).trim(), // Backend expects comment field
      date: data.date || new Date().toISOString(),
      imageNames: data.imageNames || [], // Primary field: image names for database storage
      imageUrls: data.imageUrls || [] // Optional: for backward compatibility
    };

    // Backend requires all rating fields - ensure they are always present and valid
    // Use individual ratings if provided, otherwise use overall rating as fallback (minimum 1)
    const fallbackRating = Math.max(Math.round(data.rating), 1); // Ensure minimum rating of 1

    reviewPayload.serviceRating = (data.serviceRating && data.serviceRating >= 1 && data.serviceRating <= 5)
      ? Math.round(data.serviceRating)
      : fallbackRating;

    reviewPayload.qualityRating = (data.qualityRating && data.qualityRating >= 1 && data.qualityRating <= 5)
      ? Math.round(data.qualityRating)
      : fallbackRating;

    reviewPayload.valueRating = (data.valueRating && data.valueRating >= 1 && data.valueRating <= 5)
      ? Math.round(data.valueRating)
      : fallbackRating;

    reviewPayload.communicationRating = (data.communicationRating && data.communicationRating >= 1 && data.communicationRating <= 5)
      ? Math.round(data.communicationRating)
      : fallbackRating;

    reviewPayload.timelinessRating = (data.timelinessRating && data.timelinessRating >= 1 && data.timelinessRating <= 5)
      ? Math.round(data.timelinessRating)
      : fallbackRating;

    // Add user information if available
    if (data.userName) {
      reviewPayload.userName = data.userName.trim();
    }
    if (data.userEmail) {
      reviewPayload.userEmail = data.userEmail.trim();
    }
    if (data.userProfileImage) {
      reviewPayload.userProfileImage = data.userProfileImage.trim();
    }
    if (typeof data.isVerified === 'boolean') {
      reviewPayload.isVerified = data.isVerified;
    }

    console.log('Sending review payload to backend:', {
      ...reviewPayload,
      imageNamesCount: reviewPayload.imageNames.length,
      ratingFields: {
        rating: reviewPayload.rating,
        serviceRating: reviewPayload.serviceRating,
        qualityRating: reviewPayload.qualityRating,
        valueRating: reviewPayload.valueRating,
        communicationRating: reviewPayload.communicationRating,
        timelinessRating: reviewPayload.timelinessRating
      }
    });

    const response = await apiClient.post(Path.reviews, reviewPayload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Review created successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error creating review:', error);

    // Enhanced error logging for debugging
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request error:', error.request);
    } else {
      console.error('Error message:', error.message);
    }

    logger.error('Error creating review:', error);
    throw error;
  }
};

/**
 * Get all reviews with pagination
 */
export const getAllReviews = async (params: PaginationParams = {}): Promise<ReviewsResponse> => {
  try {
    const { page = 1, limit = 3, userId } = params;
    console.log(`Fetching reviews - page: ${page}, limit: ${limit}, userId: ${userId}`);

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (userId) {
      queryParams.append('userId', userId);
    }

    const response = await apiClient.get(`${Path.reviews}?${queryParams.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching reviews:', error);
    logger.error('Error fetching reviews:', error);
    throw error;
  }
};

/**
 * Get a specific review by ID
 */
export const getReviewById = async (reviewId: string): Promise<Review> => {
  try {
    console.log(`Fetching review with ID: ${reviewId}`);
    const response = await apiClient.get(`${Path.reviews}/${reviewId}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching review ${reviewId}:`, error);
    logger.error(`Error fetching review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Update a review with image names support
 */
export const updateReview = async (reviewId: string, data: UpdateReviewData): Promise<Review> => {
  try {
    console.log(`Updating review ${reviewId} with data:`, {
      ...data,
      imageCount: data.imageNames?.length || 0,
      hasImageNames: !!(data.imageNames && data.imageNames.length > 0)
    });

    // Prepare update payload with image names
    const updatePayload = {
      ...data,
      imageNames: data.imageNames || [], // Ensure imageNames is always an array
      imageUrls: data.imageUrls // Keep for backward compatibility
    };

    console.log('Sending update payload to backend:', {
      reviewId,
      imageNamesCount: updatePayload.imageNames.length
    });

    const response = await apiClient.put(`${Path.reviews}/${reviewId}`, updatePayload);

    console.log('Review updated successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating review ${reviewId}:`, error);
    logger.error(`Error updating review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Delete a review with force option to bypass validation
 */
export const deleteReview = async (reviewId: string, force: boolean = false): Promise<void> => {
  try {
    console.log(`Deleting review with ID: ${reviewId}, force: ${force}`);

    // Validate reviewId format
    if (!reviewId || reviewId.trim() === '' || reviewId === 'undefined') {
      throw new Error('Invalid review ID provided');
    }

    // Additional validation for MongoDB ObjectId format (24 hex characters)
    const objectIdRegex = /^[0-9a-fA-F]{24}$/;
    if (!objectIdRegex.test(reviewId)) {
      console.warn(`Review ID ${reviewId} does not match MongoDB ObjectId format`);
    }

    // Try different delete endpoints
    const deleteEndpoints = [
      `${Path.reviews}/${reviewId}`,
      `${Path.reviews}/${reviewId}?force=true`,
      `/api/v1/reviews/${reviewId}?force=true`,
      `/user/api/v1/reviews/${reviewId}`,
      `/service/api/v1/reviews/${reviewId}`
    ];

    let lastError: any = null;

    for (const endpoint of deleteEndpoints) {
      try {
        console.log(`Attempting DELETE: ${endpoint}`);
        const response = await apiClient.delete(endpoint);
        console.log(`✅ Review deleted successfully via ${endpoint}:`, response.status);
        return;
      } catch (error: any) {
        console.log(`❌ DELETE failed for ${endpoint}:`, error.response?.status, error.response?.data?.message);
        lastError = error;

        // If this is a validation error, try the next endpoint
        if (error.response?.data?.message?.includes('reviewerInfo.name') ||
            error.response?.data?.message?.includes('validation')) {
          continue;
        }

        // If it's a 404, the review might not exist
        if (error.response?.status === 404) {
          console.log('Review not found, considering it already deleted');
          return;
        }
      }
    }

    // If all endpoints failed, throw the last error
    throw lastError;

  } catch (error: any) {
    console.error(`Error deleting review ${reviewId}:`, error);
    console.error('Error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method,
      baseURL: error.config?.baseURL
    });
    logger.error(`Error deleting review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Debug function to test review deletion with detailed logging
 */
export const debugDeleteReview = async (reviewId: string): Promise<void> => {
  console.log('=== DEBUG DELETE REVIEW ===');
  console.log('Review ID:', reviewId);
  console.log('Review ID type:', typeof reviewId);
  console.log('Review ID length:', reviewId?.length);
  console.log('API Base URL:', import.meta.env.VITE_APP_BACKEND_PORT);
  console.log('Reviews Path:', Path.reviews);

  // Comprehensive list of possible delete endpoints to try
  const deleteEndpoints = [
    // Standard endpoints
    `${Path.reviews}/${reviewId}`,
    `${Path.reviews}/${reviewId}?force=true`,
    `${Path.reviews}/${reviewId}?skipValidation=true`,

    // Alternative API paths
    `/api/v1/reviews/${reviewId}`,
    `/api/v1/reviews/${reviewId}?force=true`,
    `/user/api/v1/reviews/${reviewId}`,
    `/service/api/v1/reviews/${reviewId}`,
    `/reviews/${reviewId}`,
    `/reviews/${reviewId}?force=true`,

    // Direct MongoDB delete (if supported)
    `/api/v1/reviews/${reviewId}/force-delete`,
    `/admin/api/v1/reviews/${reviewId}`,
  ];

  let lastError: any = null;


  // Try each endpoint
  for (const endpoint of deleteEndpoints) {
    try {
      console.log(`🔄 Attempting DELETE: ${endpoint}`);

      const response = await apiClient.delete(endpoint);
      console.log(`✅ SUCCESS! Review deleted via: ${endpoint}`);
      console.log(`Response status: ${response.status}`);

      return; // Success!

    } catch (error: any) {
      const status = error.response?.status;
      const message = error.response?.data?.message || error.message;

      console.log(`❌ FAILED: ${endpoint}`);
      console.log(`   Status: ${status}`);
      console.log(`   Message: ${message}`);

      lastError = error;

      // Special handling for specific errors
      if (status === 404) {
        console.log('   → Review not found, might already be deleted');
        return; // Consider 404 as success (already deleted)
      }

      if (status === 403) {
        console.log('   → Forbidden, trying next endpoint...');
        continue;
      }

      if (message?.includes('reviewerInfo.name')) {
        console.log('   → Validation error detected, trying next endpoint...');
        continue;
      }

      // For other errors, continue trying
      continue;
    }
  }

  // If we get here, all endpoints failed
  console.log('❌ ALL DELETE ATTEMPTS FAILED');
  console.log('Last error details:', {
    status: lastError?.response?.status,
    statusText: lastError?.response?.statusText,
    data: lastError?.response?.data,
    message: lastError?.message
  });

  // As a last resort, try to check if the review still exists
  try {
    console.log('🔍 Checking if review still exists...');
    await apiClient.get(`${Path.reviews}/${reviewId}`);
    console.log('❌ Review still exists, delete truly failed');
    throw lastError;
  } catch (getError: any) {
    if (getError.response?.status === 404) {
      console.log('✅ Review no longer exists, considering delete successful');
      return; // Review doesn't exist anymore, so delete was successful
    }
    console.log('❌ Cannot verify review status, throwing original error');
    throw lastError;
  }
};

/**
 * Force delete a review bypassing validation (last resort)
 */
export const forceDeleteReview = async (reviewId: string): Promise<void> => {
  try {
    console.log(`Force deleting review with ID: ${reviewId}`);

    // Validate reviewId format
    if (!reviewId || reviewId.trim() === '' || reviewId === 'undefined') {
      throw new Error('Invalid review ID provided');
    }

    // Try force delete endpoints
    const forceEndpoints = [
      `${Path.reviews}/${reviewId}?force=true&skipValidation=true`,
      `/api/v1/reviews/${reviewId}/force-delete`,
      `/admin/api/v1/reviews/${reviewId}`,
      `${Path.reviews}/${reviewId}?force=true`,
    ];

    for (const endpoint of forceEndpoints) {
      try {
        console.log(`Trying force delete: ${endpoint}`);
        const response = await apiClient.delete(endpoint);
        console.log(`✅ Force delete successful via ${endpoint}:`, response.status);
        return;
      } catch (error: any) {
        console.log(`❌ Force delete failed for ${endpoint}:`, error.response?.status);
        continue;
      }
    }

    // If force delete fails, throw error
    throw new Error('All force delete attempts failed');

  } catch (error: any) {
    console.error(`Error force deleting review ${reviewId}:`, error);
    logger.error(`Error force deleting review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Fix a review that's missing required reviewerInfo field
 */
export const fixReviewerInfo = async (reviewId: string): Promise<void> => {
  try {
    console.log(`Fixing reviewerInfo for review ${reviewId}`);

    // Get the current review data
    const response = await apiClient.get(`${Path.reviews}/${reviewId}`);
    const reviewData = response.data;

    // Check if reviewerInfo is missing or incomplete
    if (!reviewData.reviewerInfo || !reviewData.reviewerInfo.name) {
      const reviewerInfo = {
        name: reviewData.userName || reviewData.name || 'Anonymous User',
        email: reviewData.userEmail || reviewData.email || '<EMAIL>'
      };

      console.log('Updating review with reviewerInfo:', reviewerInfo);

      // Update the review with the required reviewerInfo
      await apiClient.put(`${Path.reviews}/${reviewId}`, {
        reviewerInfo: reviewerInfo
      });

      console.log('Successfully fixed reviewerInfo for review:', reviewId);
    } else {
      console.log('Review already has valid reviewerInfo:', reviewData.reviewerInfo);
    }
  } catch (error: any) {
    console.error(`Error fixing reviewerInfo for review ${reviewId}:`, error);
    logger.error(`Error fixing reviewerInfo for review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Get reviews by user ID
 */
export const getReviewsByUserId = async (userId: string, params: PaginationParams = {}): Promise<ReviewsResponse> => {
  try {
    const { page = 1, limit = 10 } = params;
    console.log(`Fetching reviews for user ${userId} - page: ${page}, limit: ${limit}`);
    
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      userId: userId,
    }).toString();
    
    const response = await apiClient.get(`${Path.reviews}?${queryParams}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching reviews for user ${userId}:`, error);
    logger.error(`Error fetching reviews for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Get reviews by service ID
 */
export const getReviewsByServiceId = async (serviceId: string, params: PaginationParams = {}): Promise<ReviewsResponse> => {
  try {
    const { page = 1, limit = 10 } = params;
    console.log(`Fetching reviews for service ${serviceId} - page: ${page}, limit: ${limit}`);

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      serviceId: serviceId,
    }).toString();

    const response = await apiClient.get(`${Path.reviews}?${queryParams}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching reviews for service ${serviceId}:`, error);
    logger.error(`Error fetching reviews for service ${serviceId}:`, error);
    throw error;
  }
};

// Reply interfaces
export interface Reply {
  id?: string;
  _id?: string;
  reviewId: string;
  authorName: string;
  authorId?: string;
  authorAvatar?: string;
  content: string;
  date: string;
  isBusinessOwner?: boolean;
  likes: number;
  dislikes: number;
  userReaction?: 'like' | 'dislike' | null;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateReplyData {
  authorName: string;
  content: string;
  isBusinessOwner?: boolean;
}

export interface UpdateReplyData {
  content?: string;
}

export interface RepliesResponse {
  replies: Reply[];
  total: number;
  reviewId: string;
}

/**
 * Add reply to a review
 */
export const addReplyToReview = async (reviewId: string, data: CreateReplyData): Promise<Reply> => {
  try {
    console.log(`Adding reply to review ${reviewId}:`, data);

    const response = await apiClient.post(`${Path.reviews}/${reviewId}/reply`, data, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Reply added successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error adding reply to review ${reviewId}:`, error);
    logger.error(`Error adding reply to review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Get all replies for a review
 */
export const getRepliesForReview = async (reviewId: string): Promise<RepliesResponse> => {
  try {
    console.log(`Fetching replies for review ${reviewId}`);

    const response = await apiClient.get(`${Path.reviews}/${reviewId}/replies`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching replies for review ${reviewId}:`, error);
    logger.error(`Error fetching replies for review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Update a specific reply
 */
export const updateReply = async (reviewId: string, replyId: string, data: UpdateReplyData): Promise<Reply> => {
  try {
    console.log(`Updating reply ${replyId} for review ${reviewId}:`, data);

    const response = await apiClient.put(`${Path.reviews}/${reviewId}/reply/${replyId}`, data, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Reply updated successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating reply ${replyId}:`, error);
    logger.error(`Error updating reply ${replyId}:`, error);
    throw error;
  }
};

/**
 * Delete a specific reply
 */
export const deleteReply = async (reviewId: string, replyId: string): Promise<void> => {
  try {
    console.log(`Deleting reply ${replyId} from review ${reviewId}`);

    await apiClient.delete(`${Path.reviews}/${reviewId}/reply/${replyId}`);
    console.log('Reply deleted successfully');
  } catch (error: any) {
    console.error(`Error deleting reply ${replyId}:`, error);
    logger.error(`Error deleting reply ${replyId}:`, error);
    throw error;
  }
};

// ===== NEW API ENDPOINTS =====

// Analytics and Rating interfaces
export interface ReviewAnalytics {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  serviceRating: number;
  qualityRating: number;
  valueRating: number;
  communicationRating: number;
  timelinessRating: number;
  monthlyTrends: Array<{
    month: string;
    count: number;
    averageRating: number;
  }>;
}

export interface OverallRating {
  providerId?: string;
  serviceId?: string;
  averageRating: number;
  totalReviews: number;
  ratingBreakdown: {
    serviceRating: number;
    qualityRating: number;
    valueRating: number;
    communicationRating: number;
    timelinessRating: number;
  };
}

export interface MultipleRatingsRequest {
  providerIds?: string[];
  serviceIds?: string[];
}

export interface MultipleRatingsResponse {
  ratings: Array<{
    id: string;
    type: 'provider' | 'service';
    averageRating: number;
    totalReviews: number;
  }>;
}

// Provider Response interfaces
export interface ProviderResponse {
  id?: string;
  _id?: string;
  reviewId: string;
  providerId: string;
  providerName: string;
  response: string;
  date: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateProviderResponseData {
  response: string;
  providerName: string;
  providerId: string;
}

/**
 * Get reviews with enhanced frontend data
 */
export const getReviewsForFrontend = async (params: PaginationParams & {
  providerId?: string;
  serviceId?: string;
  rating?: number;
  sortBy?: 'date' | 'rating' | 'helpful';
  sortOrder?: 'asc' | 'desc';
} = {}): Promise<ReviewsResponse> => {
  try {
    const { page = 1, limit = 3, userId, providerId, serviceId, rating, sortBy, sortOrder } = params;
    console.log(`Fetching frontend reviews - page: ${page}, limit: ${limit}`);

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (userId) queryParams.append('userId', userId);
    if (providerId) queryParams.append('providerId', providerId);
    if (serviceId) queryParams.append('serviceId', serviceId);
    if (rating) queryParams.append('rating', rating.toString());
    if (sortBy) queryParams.append('sortBy', sortBy);
    if (sortOrder) queryParams.append('sortOrder', sortOrder);

    const response = await apiClient.get(`${Path.reviews}/frontend?${queryParams.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching frontend reviews:', error);
    logger.error('Error fetching frontend reviews:', error);
    throw error;
  }
};

/**
 * Get review analytics
 */
export const getReviewAnalytics = async (params: {
  providerId?: string;
  serviceId?: string;
  startDate?: string;
  endDate?: string;
} = {}): Promise<ReviewAnalytics> => {
  try {
    console.log('Fetching review analytics:', params);

    const queryParams = new URLSearchParams();
    if (params.providerId) queryParams.append('providerId', params.providerId);
    if (params.serviceId) queryParams.append('serviceId', params.serviceId);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);

    const response = await apiClient.get(`${Path.reviews}/analytics?${queryParams.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching review analytics:', error);
    logger.error('Error fetching review analytics:', error);
    throw error;
  }
};

/**
 * Get overall rating summary
 */
export const getOverallRating = async (params: {
  providerId?: string;
  serviceId?: string;
}): Promise<OverallRating> => {
  try {
    console.log('Fetching overall rating:', params);

    const queryParams = new URLSearchParams();
    if (params.providerId) queryParams.append('providerId', params.providerId);
    if (params.serviceId) queryParams.append('serviceId', params.serviceId);

    const response = await apiClient.get(`${Path.reviews}/rating/overall?${queryParams.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching overall rating:', error);
    logger.error('Error fetching overall rating:', error);
    throw error;
  }
};

/**
 * Get multiple rating summaries
 */
export const getMultipleRatings = async (data: MultipleRatingsRequest): Promise<MultipleRatingsResponse> => {
  try {
    console.log('Fetching multiple ratings:', data);

    const response = await apiClient.post(`${Path.reviews}/rating/multiple`, data, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response.data;
  } catch (error: any) {
    console.error('Error fetching multiple ratings:', error);
    logger.error('Error fetching multiple ratings:', error);
    throw error;
  }
};

/**
 * Add provider response to a review
 */
export const addProviderResponse = async (reviewId: string, data: CreateProviderResponseData): Promise<ProviderResponse> => {
  try {
    console.log(`Adding provider response to review ${reviewId}:`, data);

    const response = await apiClient.post(`${Path.reviews}/${reviewId}/response`, data, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Provider response added successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error adding provider response to review ${reviewId}:`, error);
    logger.error(`Error adding provider response to review ${reviewId}:`, error);
    throw error;
  }
};



// ===== ENHANCED REVIEW FILTERING AND SEARCH =====

export interface ReviewFilters {
  providerId?: string;
  serviceId?: string;
  userId?: string;
  rating?: number;
  minRating?: number;
  maxRating?: number;
  startDate?: string;
  endDate?: string;
  hasImages?: boolean;
  isVerified?: boolean;
  sortBy?: 'date' | 'rating' | 'helpful' | 'recent';
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

/**
 * Get filtered reviews with advanced search
 */
export const getFilteredReviews = async (
  filters: ReviewFilters,
  pagination: PaginationParams = {}
): Promise<ReviewsResponse> => {
  try {
    const { page = 1, limit = 10 } = pagination;
    console.log('Fetching filtered reviews:', { filters, page, limit });

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    // Add filter parameters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get(`${Path.reviews}?${queryParams.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching filtered reviews:', error);
    logger.error('Error fetching filtered reviews:', error);
    throw error;
  }
};

/**
 * Bulk operations for reviews
 */
export const bulkDeleteReviews = async (reviewIds: string[]): Promise<{ deleted: number; failed: string[] }> => {
  try {
    console.log(`Bulk deleting ${reviewIds.length} reviews:`, reviewIds);

    const response = await apiClient.delete(`${Path.reviews}/bulk`, {
      data: { reviewIds },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Bulk delete completed:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error in bulk delete reviews:', error);
    logger.error('Error in bulk delete reviews:', error);
    throw error;
  }
};

/**
 * Export reviews data
 */
export const exportReviews = async (filters: ReviewFilters = {}, format: 'csv' | 'json' = 'csv'): Promise<Blob> => {
  try {
    console.log('Exporting reviews:', { filters, format });

    const queryParams = new URLSearchParams({ format });

    // Add filter parameters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get(`${Path.reviews}/export?${queryParams.toString()}`, {
      responseType: 'blob',
    });

    return response.data;
  } catch (error: any) {
    console.error('Error exporting reviews:', error);
    logger.error('Error exporting reviews:', error);
    throw error;
  }
};
